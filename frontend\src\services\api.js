import axios from 'axios'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - remove token and redirect to login
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    
    return Promise.reject(error)
  }
)

// API Service Class
class ApiService {
  // Set auth token
  setAuthToken(token) {
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`
    } else {
      delete api.defaults.headers.common['Authorization']
    }
  }

  // Remove auth token
  removeAuthToken() {
    delete api.defaults.headers.common['Authorization']
  }

  // Generic HTTP methods
  async get(url, config = {}) {
    return await api.get(url, config)
  }

  async post(url, data = {}, config = {}) {
    return await api.post(url, data, config)
  }

  async put(url, data = {}, config = {}) {
    return await api.put(url, data, config)
  }

  async patch(url, data = {}, config = {}) {
    return await api.patch(url, data, config)
  }

  async delete(url, config = {}) {
    return await api.delete(url, config)
  }

  // User API methods
  async loginUser(credentials) {
    return await this.post('/users/login', credentials)
  }

  async signupUser(userData) {
    return await this.post('/users/signup', userData)
  }

  async getUserProfile() {
    return await this.get('/users/profile')
  }

  async updateUser(userId, userData) {
    return await this.put(`/users/${userId}`, userData)
  }

  async getUserByEmail(email) {
    return await this.get(`/users/by-email?email=${encodeURIComponent(email)}`)
  }

  async getUserById(userId) {
    return await this.get(`/user/${userId}`)
  }

  // Post API methods
  async createPost(postData) {
    return await this.post('/posts', postData)
  }

  async getAllPosts() {
    return await this.get('/posts')
  }

  async getPostById(postId) {
    return await this.get(`/posts/${postId}`)
  }

  async getPostsWithDetails() {
    return await this.get('/posts/details')
  }

  async getPostsWithCommentCount() {
    return await this.get('/posts/comment-count')
  }

  async deletePost(postId) {
    return await this.delete(`/posts/${postId}`)
  }

  // Comment API methods
  async createComment(commentData) {
    return await this.post('/comments/single', commentData)
  }

  async createBulkComments(commentsData) {
    return await this.post('/comments', { comments: commentsData })
  }

  async updateComment(commentId, commentData) {
    return await this.patch(`/comments/${commentId}`, commentData)
  }

  async findOrCreateComment(commentData) {
    return await this.post('/comments/find-or-create', commentData)
  }

  async searchComments(searchWord) {
    return await this.get(`/comments/search?word=${encodeURIComponent(searchWord)}`)
  }

  async getNewestComments(postId) {
    return await this.get(`/comments/newest/${postId}`)
  }

  async getCommentDetails(commentId) {
    return await this.get(`/comments/details/${commentId}`)
  }

  // Health check
  async healthCheck() {
    return await this.get('/health')
  }
}

// Create and export API service instance
export const apiService = new ApiService()

// Export axios instance for direct use if needed
export { api }

// Default export
export default apiService
