import express from 'express';
import {
  createBulkComments,
  updateComment,
  findOrCreateComment,
  searchComments,
  getNewestComments,
  getCommentDetails,
  createComment
} from '../controllers/commentController.js';
import {
  validateComment,
  validateCommentUpdate,
  validateCommentId,
  validateSearchQuery,
  validatePostId
} from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Comment API Endpoints

// 1. Create Multiple Comments in Bulk
// POST /comments
router.post('/', authenticateToken, createBulkComments);

// 2. Update Comment Content by ID - Only owner can edit
// PATCH /comments/:commentId
// Body: {"userId":3, "content":"updated"}
router.patch('/:commentId', authenticateToken, validateCommentUpdate, updateComment);

// 3. Find or Create Comment for a Specific Post/User/Content
// POST /comments/find-or-create
router.post('/find-or-create', authenticateToken, validateComment, findOrCreateComment);

// 4. Search Comments Containing a Word & Count Matches
// GET /comments/search?word=the
router.get('/search', validateSearchQuery, searchComments);

// 5. Get 3 Most Recent Comments for a Post
// GET /comments/newest/:postId
router.get('/newest/:postId', validatePostId, getNewestComments);

// 6. Get Comment by ID with User & Post Information
// GET /comments/details/:id
router.get('/details/:id', validateCommentId, getCommentDetails);

// Additional endpoints for better functionality

// Create single comment
// POST /comments/single
router.post('/single', authenticateToken, validateComment, createComment);

export default router;
