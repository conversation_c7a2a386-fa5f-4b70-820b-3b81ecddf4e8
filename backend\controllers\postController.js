import { Post, User, Comment } from '../models/index.js';

// Create New Post (using instance + save)
export const createPost = async (req, res) => {
  try {
    const { title, content } = req.body;
    const userId = req.user.id; // From authentication middleware

    // Create post using instance + save method
    const post = new Post({
      title,
      content,
      userId
    });

    // Save the post (this will trigger validations)
    await post.save();

    // Fetch the post with user information
    const postWithUser = await Post.findByPk(post.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Post created successfully',
      data: {
        post: postWithUser
      }
    });

  } catch (error) {
    console.error('Create post error:', error);
    
    // Handle Sequelize validation errors
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during post creation'
    });
  }
};

// Delete Post by ID - Only owner can delete
export const deletePost = async (req, res) => {
  try {
    const { postId } = req.params;
    const userId = req.user.id;

    // Find the post
    const post = await Post.findByPk(postId);

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    // Check if user owns the post or is admin
    if (!post.isOwnedBy(userId) && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied - you can only delete your own posts'
      });
    }

    // Delete the post (soft delete due to paranoid mode)
    await post.destroy();

    res.status(200).json({
      success: true,
      message: 'Post deleted successfully'
    });

  } catch (error) {
    console.error('Delete post error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during post deletion'
    });
  }
};

// Get All Posts with User & Comments Details
export const getPostsWithDetails = async (req, res) => {
  try {
    const posts = await Post.findAll({
      attributes: ['id', 'title'],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        },
        {
          model: Comment,
          as: 'comments',
          attributes: ['id', 'content'],
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'name']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.status(200).json({
      success: true,
      message: 'Posts with details retrieved successfully',
      data: {
        posts,
        count: posts.length
      }
    });

  } catch (error) {
    console.error('Get posts with details error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during posts retrieval'
    });
  }
};

// Get All Posts with Comment Count
export const getPostsWithCommentCount = async (req, res) => {
  try {
    const posts = await Post.findAll({
      attributes: [
        'id',
        'title',
        'content',
        'userId',
        'createdAt',
        'updatedAt',
        [
          Post.sequelize.fn('COUNT', Post.sequelize.col('comments.id')),
          'commentCount'
        ]
      ],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        },
        {
          model: Comment,
          as: 'comments',
          attributes: []
        }
      ],
      group: ['Post.id', 'user.id'],
      order: [['createdAt', 'DESC']]
    });

    res.status(200).json({
      success: true,
      message: 'Posts with comment count retrieved successfully',
      data: {
        posts,
        count: posts.length
      }
    });

  } catch (error) {
    console.error('Get posts with comment count error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during posts retrieval'
    });
  }
};

// Get all posts (general endpoint)
export const getAllPosts = async (req, res) => {
  try {
    const posts = await Post.findAll({
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.status(200).json({
      success: true,
      message: 'Posts retrieved successfully',
      data: {
        posts,
        count: posts.length
      }
    });

  } catch (error) {
    console.error('Get all posts error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during posts retrieval'
    });
  }
};

// Get post by ID
export const getPostById = async (req, res) => {
  try {
    const { postId } = req.params;

    const post = await Post.findByPk(postId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        },
        {
          model: Comment,
          as: 'comments',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'name']
            }
          ],
          order: [['createdAt', 'DESC']]
        }
      ]
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Post retrieved successfully',
      data: {
        post
      }
    });

  } catch (error) {
    console.error('Get post by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during post retrieval'
    });
  }
};
