import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Activity, 
  FileText, 
  MessageSquare, 
  User, 
  Calendar,
  ExternalLink,
  RefreshCw
} from 'lucide-react'
import { formatRelativeTime, truncateText } from '../../utils/helpers'
import { useAuth } from '../../context/AuthContext'

const ActivityFeed = ({ activities = [], loading = false, onRefresh }) => {
  const { user } = useAuth()

  // Generate mock activities if none provided
  const mockActivities = [
    {
      id: 1,
      type: 'post_created',
      title: 'You created a new post',
      description: 'Getting Started with React Hooks',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      link: '/posts/1'
    },
    {
      id: 2,
      type: 'comment_added',
      title: 'You commented on a post',
      description: 'Great article! Very helpful for beginners.',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      link: '/posts/2'
    },
    {
      id: 3,
      type: 'user_joined',
      title: 'Welcome to the community!',
      description: 'You joined PostHub and can now create posts and comments.',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      link: '/profile'
    }
  ]

  const displayActivities = activities.length > 0 ? activities : mockActivities

  const getActivityIcon = (type) => {
    switch (type) {
      case 'post_created':
        return { icon: FileText, color: 'bg-blue-500' }
      case 'comment_added':
        return { icon: MessageSquare, color: 'bg-green-500' }
      case 'user_joined':
        return { icon: User, color: 'bg-purple-500' }
      default:
        return { icon: Activity, color: 'bg-gray-500' }
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex space-x-3 animate-pulse">
            <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Recent Activity
        </h3>
        {onRefresh && (
          <button
            onClick={onRefresh}
            className="btn btn-ghost btn-sm"
            title="Refresh activity"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Activity List */}
      {displayActivities.length === 0 ? (
        <div className="text-center py-8">
          <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No recent activity</p>
        </div>
      ) : (
        <div className="space-y-4">
          {displayActivities.map((activity) => {
            const { icon: Icon, color } = getActivityIcon(activity.type)
            
            return (
              <div key={activity.id} className="flex space-x-3 group">
                <div className={`w-8 h-8 ${color} rounded-full flex items-center justify-center flex-shrink-0`}>
                  <Icon className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.title}
                      </p>
                      {activity.description && (
                        <p className="text-sm text-gray-600 mt-1">
                          {truncateText(activity.description, 80)}
                        </p>
                      )}
                      <div className="flex items-center space-x-2 mt-2 text-xs text-gray-500">
                        <Calendar className="w-3 h-3" />
                        <span>{formatRelativeTime(activity.timestamp)}</span>
                      </div>
                    </div>
                    {activity.link && (
                      <Link
                        to={activity.link}
                        className="opacity-0 group-hover:opacity-100 transition-opacity text-primary-600 hover:text-primary-700 ml-2"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* View All Link */}
      {displayActivities.length > 0 && (
        <div className="text-center pt-4 border-t border-gray-200">
          <Link
            to="/activity"
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            View all activity
          </Link>
        </div>
      )}
    </div>
  )
}

export default ActivityFeed
