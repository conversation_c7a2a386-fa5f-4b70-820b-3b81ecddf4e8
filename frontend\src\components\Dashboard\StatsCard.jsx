import { TrendingUp, TrendingDown } from 'lucide-react'

const StatsCard = ({ 
  title, 
  value, 
  icon: Icon, 
  color = 'bg-blue-500', 
  change, 
  changeType = 'positive',
  loading = false 
}) => {
  if (loading) {
    return (
      <div className="card animate-pulse">
        <div className="card-body">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
            <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    )
  }

  const isPositive = changeType === 'positive'
  const changeColor = isPositive ? 'text-success-600' : 'text-error-600'
  const TrendIcon = isPositive ? TrendingUp : TrendingDown

  return (
    <div className="card hover:shadow-medium transition-shadow">
      <div className="card-body">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">
              {title}
            </p>
            <p className="text-2xl font-bold text-gray-900 mb-1">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {change && (
              <div className={`flex items-center text-sm ${changeColor}`}>
                <TrendIcon className="w-4 h-4 mr-1" />
                <span>{change}</span>
                <span className="text-gray-500 ml-1">from last month</span>
              </div>
            )}
          </div>
          <div className={`w-12 h-12 ${color} rounded-lg flex items-center justify-center`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </div>
  )
}

export default StatsCard
