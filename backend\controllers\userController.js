import { User } from '../models/index.js';
import { generateToken } from '../middleware/auth.js';

// Login - Authenticate user
export const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Check password
    const isPasswordValid = await user.checkPassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Generate JWT token
    const token = generateToken(user.id);

    // Return user data without password
    const userData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: userData,
        token
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during login'
    });
  }
};

// Sign Up - Create a new user using build & save
export const signUp = async (req, res) => {
  try {
    const { name, email, password, role = 'user' } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Create user using build & save method
    const user = User.build({
      name,
      email,
      password,
      role
    });

    // Save the user (this will trigger validations and hooks)
    await user.save();

    // Generate JWT token
    const token = generateToken(user.id);

    // Return user data without password
    const userData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: {
        user: userData,
        token
      }
    });

  } catch (error) {
    console.error('Sign up error:', error);

    // Handle Sequelize validation errors
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    // Handle unique constraint errors
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: 'Email address already exists'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during user creation'
    });
  }
};

// Update or Create User by ID (Skip validation)
export const updateOrCreateUserById = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, email, password, role } = req.body;

    // Use upsert to update or create user
    const [user, created] = await User.upsert({
      id: parseInt(id),
      name,
      email,
      password,
      role
    }, {
      returning: true,
      validate: false // Skip validation as requested
    });

    // Return user data without password
    const userData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.status(created ? 201 : 200).json({
      success: true,
      message: created ? 'User created successfully' : 'User updated successfully',
      data: {
        user: userData,
        created
      }
    });

  } catch (error) {
    console.error('Update or create user error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during user update/create'
    });
  }
};

// Find User by Email
export const findUserByEmail = async (req, res) => {
  try {
    const { email } = req.query;

    const user = await User.findByEmail(email);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found with this email'
      });
    }

    // Return user data without password
    const userData = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.status(200).json({
      success: true,
      message: 'User found successfully',
      data: {
        user: userData
      }
    });

  } catch (error) {
    console.error('Find user by email error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during user search'
    });
  }
};

// Get User by ID (without role field)
export const getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      attributes: ['id', 'name', 'email', 'createdAt', 'updatedAt'] // Exclude role and password
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'User retrieved successfully',
      data: {
        user
      }
    });

  } catch (error) {
    console.error('Get user by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during user retrieval'
    });
  }
};
