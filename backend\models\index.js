import { sequelize } from '../config/database.js';
import User from './User.js';
import Post from './Post.js';
import Comment from './Comment.js';

// Define relationships between models

// User-Post relationships
User.hasMany(Post, {
  foreignKey: 'userId',
  as: 'posts',
  onDelete: 'CASCADE'
});

Post.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

// User-Comment relationships
User.hasMany(Comment, {
  foreignKey: 'userId',
  as: 'comments',
  onDelete: 'CASCADE'
});

Comment.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

// Post-Comment relationships
Post.hasMany(Comment, {
  foreignKey: 'postId',
  as: 'comments',
  onDelete: 'CASCADE'
});

Comment.belongsTo(Post, {
  foreignKey: 'postId',
  as: 'post'
});

// Sync database function
const syncDatabase = async (force = false) => {
  try {
    await sequelize.sync({ force });
    console.log(`✅ Database synced successfully${force ? ' (forced)' : ''}`);
  } catch (error) {
    console.error('❌ Error syncing database:', error.message);
    throw error;
  }
};

// Export models and database utilities
export {
  sequelize,
  User,
  Post,
  Comment,
  syncDatabase
};

// Default export for convenience
export default {
  sequelize,
  User,
  Post,
  Comment,
  syncDatabase
};
