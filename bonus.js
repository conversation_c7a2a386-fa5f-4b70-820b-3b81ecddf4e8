/**
 * LeetCode Problem: Remove Element
 * 
 * Given an integer array nums and an integer val, remove all occurrences of val in nums in-place.
 * The order of the elements may be changed. Then return the number of elements in nums which are not equal to val.
 * 
 * Consider the number of elements in nums which are not equal to val be k, to get accepted, you need to do the following things:
 * - Change the array nums such that the first k elements of nums contain the elements which are not equal to val.
 * - The remaining elements of nums are not important as well as the size of nums.
 * - Return k.
 * 
 * Example 1:
 * Input: nums = [3,2,2,3], val = 3
 * Output: 2, nums = [2,2,_,_]
 * Explanation: Your function should return k = 2, with the first two elements of nums being 2.
 * It does not matter what you leave beyond the returned k (hence they are underscores).
 * 
 * Example 2:
 * Input: nums = [0,1,2,2,3,0,4,2], val = 2
 * Output: 5, nums = [0,1,4,0,3,_,_,_]
 * Explanation: Your function should return k = 5, with the first five elements of nums containing 0, 0, 1, 3, and 4.
 * Note that the five elements can be returned in any order.
 * It does not matter what you leave beyond the returned k (hence they are underscores).
 */

/**
 * Solution 1: Two Pointers Approach (Optimal)
 * Time Complexity: O(n)
 * Space Complexity: O(1)
 * 
 * @param {number[]} nums - The input array
 * @param {number} val - The value to remove
 * @return {number} - The number of elements not equal to val
 */
const removeElement = (nums, val) => {
    let k = 0; // Pointer for the position to place non-val elements
    
    for (let i = 0; i < nums.length; i++) {
        if (nums[i] !== val) {
            nums[k] = nums[i];
            k++;
        }
    }
    
    return k;
};

/**
 * Solution 2: Two Pointers from Both Ends (Alternative)
 * Time Complexity: O(n)
 * Space Complexity: O(1)
 * 
 * This approach is more efficient when the elements to remove are rare.
 * 
 * @param {number[]} nums - The input array
 * @param {number} val - The value to remove
 * @return {number} - The number of elements not equal to val
 */
const removeElementOptimized = (nums, val) => {
    let left = 0;
    let right = nums.length;
    
    while (left < right) {
        if (nums[left] === val) {
            nums[left] = nums[right - 1];
            right--;
        } else {
            left++;
        }
    }
    
    return left;
};

/**
 * Solution 3: Filter Approach (Functional Programming Style)
 * Time Complexity: O(n)
 * Space Complexity: O(n) - Creates a new array
 * 
 * Note: This doesn't modify the original array in-place as required by the problem,
 * but demonstrates a functional programming approach.
 * 
 * @param {number[]} nums - The input array
 * @param {number} val - The value to remove
 * @return {number} - The number of elements not equal to val
 */
const removeElementFunctional = (nums, val) => {
    const filtered = nums.filter(num => num !== val);
    
    // Copy filtered elements back to original array
    for (let i = 0; i < filtered.length; i++) {
        nums[i] = filtered[i];
    }
    
    return filtered.length;
};

// Test cases
const testRemoveElement = () => {
    console.log('🧪 Testing Remove Element Solutions...\n');
    
    // Test Case 1
    const nums1 = [3, 2, 2, 3];
    const val1 = 3;
    const result1 = removeElement([...nums1], val1);
    console.log(`Test 1: nums = [${nums1.join(', ')}], val = ${val1}`);
    console.log(`Expected: 2, Got: ${result1}`);
    console.log(`✅ ${result1 === 2 ? 'PASSED' : 'FAILED'}\n`);
    
    // Test Case 2
    const nums2 = [0, 1, 2, 2, 3, 0, 4, 2];
    const val2 = 2;
    const result2 = removeElement([...nums2], val2);
    console.log(`Test 2: nums = [${nums2.join(', ')}], val = ${val2}`);
    console.log(`Expected: 5, Got: ${result2}`);
    console.log(`✅ ${result2 === 5 ? 'PASSED' : 'FAILED'}\n`);
    
    // Test Case 3: Empty array
    const nums3 = [];
    const val3 = 1;
    const result3 = removeElement([...nums3], val3);
    console.log(`Test 3: nums = [], val = ${val3}`);
    console.log(`Expected: 0, Got: ${result3}`);
    console.log(`✅ ${result3 === 0 ? 'PASSED' : 'FAILED'}\n`);
    
    // Test Case 4: All elements are the target value
    const nums4 = [2, 2, 2, 2];
    const val4 = 2;
    const result4 = removeElement([...nums4], val4);
    console.log(`Test 4: nums = [${nums4.join(', ')}], val = ${val4}`);
    console.log(`Expected: 0, Got: ${result4}`);
    console.log(`✅ ${result4 === 0 ? 'PASSED' : 'FAILED'}\n`);
    
    // Test Case 5: No elements match the target value
    const nums5 = [1, 3, 5, 7];
    const val5 = 2;
    const result5 = removeElement([...nums5], val5);
    console.log(`Test 5: nums = [${nums5.join(', ')}], val = ${val5}`);
    console.log(`Expected: 4, Got: ${result5}`);
    console.log(`✅ ${result5 === 4 ? 'PASSED' : 'FAILED'}\n`);
    
    console.log('🎉 All tests completed!');
};

// Performance comparison
const performanceTest = () => {
    console.log('\n⚡ Performance Comparison...\n');
    
    const largeArray = Array.from({ length: 100000 }, (_, i) => i % 10);
    const targetVal = 5;
    
    // Test Solution 1
    console.time('Two Pointers (Standard)');
    removeElement([...largeArray], targetVal);
    console.timeEnd('Two Pointers (Standard)');
    
    // Test Solution 2
    console.time('Two Pointers (Optimized)');
    removeElementOptimized([...largeArray], targetVal);
    console.timeEnd('Two Pointers (Optimized)');
    
    // Test Solution 3
    console.time('Functional Approach');
    removeElementFunctional([...largeArray], targetVal);
    console.timeEnd('Functional Approach');
};

// Export functions for use in other modules
export {
    removeElement,
    removeElementOptimized,
    removeElementFunctional,
    testRemoveElement,
    performanceTest
};

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testRemoveElement();
    performanceTest();
}
