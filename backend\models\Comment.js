import { DataTypes, Model, Op } from 'sequelize';
import { sequelize } from '../config/database.js';

// Comments Model defined using init method
class Comment extends Model {
  // Instance method to check if user owns this comment
  isOwnedBy(userId) {
    return this.userId === userId;
  }

  // Static method to search comments containing a word and count matches
  static async searchCommentsWithCount(searchWord) {
    const comments = await this.findAll({
      where: {
        content: {
          [Op.like]: `%${searchWord}%`
        }
      },
      include: [
        {
          model: sequelize.models.User,
          attributes: ['id', 'name']
        },
        {
          model: sequelize.models.Post,
          attributes: ['id', 'title']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    return {
      comments,
      count: comments.length
    };
  }

  // Static method to get newest comments for a post
  static async getNewestCommentsForPost(postId, limit = 3) {
    return await this.findAll({
      where: { postId },
      include: [
        {
          model: sequelize.models.User,
          attributes: ['id', 'name']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit
    });
  }

  // Static method to get comment with full details
  static async getCommentWithDetails(commentId) {
    return await this.findByPk(commentId, {
      include: [
        {
          model: sequelize.models.User,
          attributes: ['id', 'name', 'email']
        },
        {
          model: sequelize.models.Post,
          attributes: ['id', 'title', 'content'],
          include: [
            {
              model: sequelize.models.User,
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });
  }

  // Static method for bulk create comments
  static async bulkCreateComments(commentsData) {
    return await this.bulkCreate(commentsData, {
      validate: true,
      returning: true
    });
  }

  // Static method for find or create comment
  static async findOrCreateComment(postId, userId, content) {
    return await this.findOrCreate({
      where: { postId, userId, content },
      defaults: { postId, userId, content }
    });
  }
}

Comment.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Comment content cannot be empty'
      },
      len: {
        args: [1, 1000],
        msg: 'Comment must be between 1 and 1000 characters'
      }
    }
  },
  postId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'posts',
      key: 'id'
    }
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  sequelize,
  modelName: 'Comment',
  tableName: 'comments',
  timestamps: true
});

export default Comment;
