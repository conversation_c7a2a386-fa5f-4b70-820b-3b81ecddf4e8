import { useState } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { Plus, Trash2, Send, FileText } from 'lucide-react'
import { useCreateBulkComments } from '../../hooks/useApi'
import { usePosts } from '../../hooks/useApi'

const BulkCommentCreator = ({ onSuccess, onCancel }) => {
  const [selectedPost, setSelectedPost] = useState('')
  const { data: postsData } = usePosts()
  const createBulkCommentsMutation = useCreateBulkComments()

  const posts = postsData?.data?.posts || []

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setError
  } = useForm({
    defaultValues: {
      comments: [{ content: '' }]
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'comments'
  })

  const onSubmit = async (data) => {
    if (!selectedPost) {
      setError('root', {
        type: 'manual',
        message: 'Please select a post'
      })
      return
    }

    try {
      const commentsData = data.comments
        .filter(comment => comment.content.trim())
        .map(comment => ({
          content: comment.content.trim(),
          postId: parseInt(selectedPost)
        }))

      if (commentsData.length === 0) {
        setError('root', {
          type: 'manual',
          message: 'Please add at least one comment'
        })
        return
      }

      await createBulkCommentsMutation.mutateAsync(commentsData)
      reset()
      setSelectedPost('')
      onSuccess?.()
    } catch (error) {
      setError('root', {
        type: 'manual',
        message: error.response?.data?.message || 'Failed to create comments'
      })
    }
  }

  const addComment = () => {
    append({ content: '' })
  }

  const removeComment = (index) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-gray-900">
          Create Multiple Comments
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Add multiple comments to a post at once
        </p>
      </div>
      
      <div className="card-body">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Post Selection */}
          <div>
            <label htmlFor="post" className="block text-sm font-medium text-gray-700 mb-2">
              Select Post
            </label>
            <select
              id="post"
              value={selectedPost}
              onChange={(e) => setSelectedPost(e.target.value)}
              className="input w-full"
            >
              <option value="">Choose a post...</option>
              {posts.map((post) => (
                <option key={post.id} value={post.id}>
                  {post.title}
                </option>
              ))}
            </select>
          </div>

          {/* Comments */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Comments ({fields.length})
              </label>
              <button
                type="button"
                onClick={addComment}
                className="btn btn-secondary btn-sm"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Comment
              </button>
            </div>

            <div className="space-y-4">
              {fields.map((field, index) => (
                <div key={field.id} className="relative">
                  <div className="flex items-start space-x-3">
                    <div className="flex-1">
                      <textarea
                        rows={3}
                        placeholder={`Comment ${index + 1}...`}
                        className={`input resize-none ${
                          errors.comments?.[index]?.content ? 'input-error' : ''
                        }`}
                        {...register(`comments.${index}.content`, {
                          required: false,
                          maxLength: {
                            value: 1000,
                            message: 'Comment must be less than 1000 characters'
                          }
                        })}
                      />
                      {errors.comments?.[index]?.content && (
                        <p className="mt-1 text-sm text-error-600">
                          {errors.comments[index].content.message}
                        </p>
                      )}
                    </div>
                    
                    {fields.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeComment(index)}
                        className="p-2 text-error-600 hover:text-error-700 hover:bg-error-50 rounded-lg transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Error Message */}
          {errors.root && (
            <div className="bg-error-50 border border-error-200 rounded-lg p-3">
              <p className="text-sm text-error-600">
                {errors.root.message}
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="btn btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={createBulkCommentsMutation.isLoading}
              className="btn btn-primary"
            >
              {createBulkCommentsMutation.isLoading ? (
                <div className="flex items-center">
                  <div className="spinner w-4 h-4 mr-2"></div>
                  Creating...
                </div>
              ) : (
                <div className="flex items-center">
                  <Send className="w-4 h-4 mr-2" />
                  Create Comments
                </div>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Tips */}
      <div className="card-footer">
        <div className="text-sm text-gray-600">
          <h4 className="font-medium mb-2">Tips:</h4>
          <ul className="space-y-1">
            <li>• Empty comments will be ignored</li>
            <li>• Each comment can be up to 1000 characters</li>
            <li>• All comments will be posted to the selected post</li>
            <li>• You can add or remove comment fields as needed</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default BulkCommentCreator
