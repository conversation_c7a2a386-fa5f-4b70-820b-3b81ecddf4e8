import { cn } from '../../utils/helpers'

const LoadingSpinner = ({ 
  size = 'md', 
  color = 'primary', 
  className = '',
  text = null,
  fullScreen = false 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const colorClasses = {
    primary: 'border-primary-600',
    secondary: 'border-gray-600',
    white: 'border-white',
    success: 'border-success-600',
    warning: 'border-warning-600',
    error: 'border-error-600'
  }

  const spinnerClasses = cn(
    'animate-spin rounded-full border-2 border-gray-200',
    sizeClasses[size],
    colorClasses[color],
    className
  )

  const content = (
    <div className="flex flex-col items-center justify-center space-y-3">
      <div className={spinnerClasses} style={{ borderTopColor: 'transparent' }}></div>
      {text && (
        <p className="text-sm text-gray-600 animate-pulse">
          {text}
        </p>
      )}
    </div>
  )

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
        {content}
      </div>
    )
  }

  return content
}

// Skeleton Loading Components
export const SkeletonCard = ({ className = '' }) => (
  <div className={cn('card animate-pulse', className)}>
    <div className="card-body">
      <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
      <div className="space-y-2">
        <div className="h-3 bg-gray-200 rounded"></div>
        <div className="h-3 bg-gray-200 rounded w-5/6"></div>
        <div className="h-3 bg-gray-200 rounded w-4/6"></div>
      </div>
    </div>
  </div>
)

export const SkeletonPost = ({ className = '' }) => (
  <div className={cn('card animate-pulse', className)}>
    <div className="card-body">
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
        <div className="flex-1">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
      <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
      <div className="space-y-2 mb-4">
        <div className="h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        <div className="h-4 bg-gray-200 rounded w-4/6"></div>
      </div>
      <div className="flex items-center space-x-4">
        <div className="h-3 bg-gray-200 rounded w-16"></div>
        <div className="h-3 bg-gray-200 rounded w-20"></div>
        <div className="h-3 bg-gray-200 rounded w-12"></div>
      </div>
    </div>
  </div>
)

export const SkeletonComment = ({ className = '' }) => (
  <div className={cn('card animate-pulse', className)}>
    <div className="card-body py-3">
      <div className="flex space-x-3">
        <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
        <div className="flex-1">
          <div className="h-3 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="space-y-1">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export const SkeletonList = ({ count = 3, ItemComponent = SkeletonCard, className = '' }) => (
  <div className={cn('space-y-4', className)}>
    {[...Array(count)].map((_, i) => (
      <ItemComponent key={i} />
    ))}
  </div>
)

export const SkeletonGrid = ({ 
  count = 6, 
  ItemComponent = SkeletonCard, 
  className = '',
  gridClassName = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
}) => (
  <div className={cn(gridClassName, className)}>
    {[...Array(count)].map((_, i) => (
      <ItemComponent key={i} />
    ))}
  </div>
)

export default LoadingSpinner
