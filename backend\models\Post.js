import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../config/database.js';

// Posts Model defined using init method
class Post extends Model {
  // Instance method to check if user owns this post
  isOwnedBy(userId) {
    return this.userId === userId;
  }

  // Static method to get posts with comment count
  static async getPostsWithCommentCount() {
    return await this.findAll({
      attributes: [
        'id',
        'title',
        'content',
        'userId',
        'createdAt',
        'updatedAt',
        [sequelize.fn('COUNT', sequelize.col('Comments.id')), 'commentCount']
      ],
      include: [
        {
          model: sequelize.models.Comment,
          attributes: []
        }
      ],
      group: ['Post.id'],
      raw: false
    });
  }

  // Static method to get posts with full details
  static async getPostsWithDetails() {
    return await this.findAll({
      attributes: ['id', 'title'],
      include: [
        {
          model: sequelize.models.User,
          attributes: ['id', 'name']
        },
        {
          model: sequelize.models.Comment,
          attributes: ['id', 'content'],
          include: [
            {
              model: sequelize.models.User,
              attributes: ['id', 'name']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']]
    });
  }
}

Post.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Title cannot be empty'
      },
      len: {
        args: [1, 255],
        msg: 'Title must be between 1 and 255 characters'
      }
    }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Content cannot be empty'
      },
      len: {
        args: [1, 10000],
        msg: 'Content must be between 1 and 10000 characters'
      }
    }
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  sequelize,
  modelName: 'Post',
  tableName: 'posts',
  timestamps: true,
  paranoid: true, // Enables soft delete
  deletedAt: 'deletedAt'
});

export default Post;
