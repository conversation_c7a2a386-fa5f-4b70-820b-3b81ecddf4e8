import { Link } from 'react-router-dom'
import { ArrowRight, Users, MessageSquare, FileText, Shield, Zap, Globe } from 'lucide-react'

const HomePage = () => {
  const features = [
    {
      icon: Users,
      title: 'User Management',
      description: 'Complete user registration, authentication, and profile management with role-based access control.'
    },
    {
      icon: FileText,
      title: 'Post Creation',
      description: 'Create, edit, and manage posts with rich content support and soft delete functionality.'
    },
    {
      icon: MessageSquare,
      title: 'Comment System',
      description: 'Engage with posts through comments, bulk operations, and advanced search capabilities.'
    },
    {
      icon: Shield,
      title: 'Security First',
      description: 'Built with security in mind featuring JWT authentication, input validation, and data protection.'
    },
    {
      icon: Zap,
      title: 'Modern Tech Stack',
      description: 'Powered by React, Node.js, Express, Sequelize, and modern development practices.'
    },
    {
      icon: Globe,
      title: 'Responsive Design',
      description: 'Beautiful, responsive interface that works seamlessly across all devices and screen sizes.'
    }
  ]

  const stats = [
    { label: 'Active Users', value: '10K+' },
    { label: 'Posts Created', value: '50K+' },
    { label: 'Comments', value: '200K+' },
    { label: 'Uptime', value: '99.9%' }
  ]

  return (
    <div className="space-y-20">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Modern{' '}
              <span className="text-primary-600">User Management</span>
              <br />
              Made Simple
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              A comprehensive platform for managing users, posts, and comments with 
              beautiful interfaces, powerful features, and modern development practices.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/signup"
                className="btn btn-primary btn-lg"
              >
                Get Started Free
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
              <Link
                to="/login"
                className="btn btn-secondary btn-lg"
              >
                Sign In
              </Link>
            </div>
          </div>
        </div>

        {/* Background Decoration */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-primary-100 rounded-full blur-3xl opacity-20"></div>
          <div className="absolute bottom-0 right-1/4 w-64 h-64 bg-secondary-100 rounded-full blur-3xl opacity-20"></div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything you need to manage your community
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Built with modern technologies and best practices to provide 
              a seamless experience for both users and administrators.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="card hover:shadow-medium transition-shadow duration-300">
                <div className="card-body">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary-600 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to get started?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join thousands of users who are already using our platform 
            to manage their communities effectively.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/signup"
              className="btn bg-white text-primary-600 hover:bg-gray-50 btn-lg"
            >
              Create Free Account
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
            <Link
              to="/contact"
              className="btn border-2 border-white text-white hover:bg-white hover:text-primary-600 btn-lg"
            >
              Contact Sales
            </Link>
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              See it in action
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Experience the power of our platform with a live demo. 
              No signup required.
            </p>
          </div>

          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8 md:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Try the demo
                </h3>
                <p className="text-gray-600 mb-6">
                  Explore all features with our interactive demo. Create posts, 
                  add comments, and see how easy it is to manage your community.
                </p>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Demo Email:</strong> <EMAIL></p>
                  <p><strong>Demo Password:</strong> password123</p>
                </div>
              </div>
              <div className="text-center lg:text-right">
                <Link
                  to="/login"
                  className="btn btn-primary btn-lg"
                >
                  Try Demo Now
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default HomePage
