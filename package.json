{"name": "user-post-comment-management", "version": "1.0.0", "description": "A comprehensive User, Post, and Comment Management System with Express backend and Vite frontend", "main": "index.js", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "start": "cd backend && npm start"}, "keywords": ["express", "sequelize", "react", "vite", "crud", "api"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}