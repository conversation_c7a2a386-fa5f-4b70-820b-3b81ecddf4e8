import { useQuery, useMutation, useQueryClient } from 'react-query'
import { apiService } from '../services/api'
import toast from 'react-hot-toast'

// Query Keys
export const QUERY_KEYS = {
  POSTS: 'posts',
  POST: 'post',
  POSTS_WITH_DETAILS: 'posts-with-details',
  POSTS_WITH_COMMENT_COUNT: 'posts-with-comment-count',
  COMMENTS: 'comments',
  COMMENT: 'comment',
  NEWEST_COMMENTS: 'newest-comments',
  SEARCH_COMMENTS: 'search-comments',
  USERS: 'users',
  USER: 'user',
  USER_PROFILE: 'user-profile'
}

// Posts Hooks
export const usePosts = () => {
  return useQuery(
    QUERY_KEYS.POSTS,
    () => apiService.getAllPosts(),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  )
}

export const usePost = (postId) => {
  return useQuery(
    [QUERY_KEYS.POST, postId],
    () => apiService.getPostById(postId),
    {
      enabled: !!postId,
      staleTime: 5 * 60 * 1000,
    }
  )
}

export const usePostsWithDetails = () => {
  return useQuery(
    QUERY_KEYS.POSTS_WITH_DETAILS,
    () => apiService.getPostsWithDetails(),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  )
}

export const usePostsWithCommentCount = () => {
  return useQuery(
    QUERY_KEYS.POSTS_WITH_COMMENT_COUNT,
    () => apiService.getPostsWithCommentCount(),
    {
      staleTime: 2 * 60 * 1000,
    }
  )
}

export const useCreatePost = () => {
  const queryClient = useQueryClient()
  
  return useMutation(
    (postData) => apiService.createPost(postData),
    {
      onSuccess: (data) => {
        // Invalidate and refetch posts
        queryClient.invalidateQueries(QUERY_KEYS.POSTS)
        queryClient.invalidateQueries(QUERY_KEYS.POSTS_WITH_DETAILS)
        queryClient.invalidateQueries(QUERY_KEYS.POSTS_WITH_COMMENT_COUNT)
        
        toast.success('Post created successfully!')
      },
      onError: (error) => {
        const message = error.response?.data?.message || 'Failed to create post'
        toast.error(message)
      }
    }
  )
}

export const useDeletePost = () => {
  const queryClient = useQueryClient()
  
  return useMutation(
    (postId) => apiService.deletePost(postId),
    {
      onSuccess: () => {
        // Invalidate and refetch posts
        queryClient.invalidateQueries(QUERY_KEYS.POSTS)
        queryClient.invalidateQueries(QUERY_KEYS.POSTS_WITH_DETAILS)
        queryClient.invalidateQueries(QUERY_KEYS.POSTS_WITH_COMMENT_COUNT)
        
        toast.success('Post deleted successfully!')
      },
      onError: (error) => {
        const message = error.response?.data?.message || 'Failed to delete post'
        toast.error(message)
      }
    }
  )
}

// Comments Hooks
export const useNewestComments = (postId) => {
  return useQuery(
    [QUERY_KEYS.NEWEST_COMMENTS, postId],
    () => apiService.getNewestComments(postId),
    {
      enabled: !!postId,
      staleTime: 1 * 60 * 1000, // 1 minute
    }
  )
}

export const useSearchComments = (searchWord) => {
  return useQuery(
    [QUERY_KEYS.SEARCH_COMMENTS, searchWord],
    () => apiService.searchComments(searchWord),
    {
      enabled: !!searchWord && searchWord.length > 0,
      staleTime: 2 * 60 * 1000,
    }
  )
}

export const useCommentDetails = (commentId) => {
  return useQuery(
    [QUERY_KEYS.COMMENT, commentId],
    () => apiService.getCommentDetails(commentId),
    {
      enabled: !!commentId,
      staleTime: 5 * 60 * 1000,
    }
  )
}

export const useCreateComment = () => {
  const queryClient = useQueryClient()
  
  return useMutation(
    (commentData) => apiService.createComment(commentData),
    {
      onSuccess: (data, variables) => {
        // Invalidate related queries
        queryClient.invalidateQueries([QUERY_KEYS.POST, variables.postId])
        queryClient.invalidateQueries([QUERY_KEYS.NEWEST_COMMENTS, variables.postId])
        queryClient.invalidateQueries(QUERY_KEYS.POSTS_WITH_DETAILS)
        queryClient.invalidateQueries(QUERY_KEYS.POSTS_WITH_COMMENT_COUNT)
        
        toast.success('Comment added successfully!')
      },
      onError: (error) => {
        const message = error.response?.data?.message || 'Failed to add comment'
        toast.error(message)
      }
    }
  )
}

export const useCreateBulkComments = () => {
  const queryClient = useQueryClient()
  
  return useMutation(
    (commentsData) => apiService.createBulkComments(commentsData),
    {
      onSuccess: (data) => {
        // Invalidate all comment-related queries
        queryClient.invalidateQueries(QUERY_KEYS.POSTS)
        queryClient.invalidateQueries(QUERY_KEYS.POSTS_WITH_DETAILS)
        queryClient.invalidateQueries(QUERY_KEYS.POSTS_WITH_COMMENT_COUNT)
        queryClient.invalidateQueries(QUERY_KEYS.NEWEST_COMMENTS)
        
        toast.success(`${data.data.count} comments created successfully!`)
      },
      onError: (error) => {
        const message = error.response?.data?.message || 'Failed to create comments'
        toast.error(message)
      }
    }
  )
}

export const useUpdateComment = () => {
  const queryClient = useQueryClient()
  
  return useMutation(
    ({ commentId, commentData }) => apiService.updateComment(commentId, commentData),
    {
      onSuccess: (data) => {
        // Invalidate related queries
        queryClient.invalidateQueries([QUERY_KEYS.COMMENT, data.data.comment.id])
        queryClient.invalidateQueries(QUERY_KEYS.POSTS_WITH_DETAILS)
        
        toast.success('Comment updated successfully!')
      },
      onError: (error) => {
        const message = error.response?.data?.message || 'Failed to update comment'
        toast.error(message)
      }
    }
  )
}

export const useFindOrCreateComment = () => {
  const queryClient = useQueryClient()
  
  return useMutation(
    (commentData) => apiService.findOrCreateComment(commentData),
    {
      onSuccess: (data, variables) => {
        // Invalidate related queries
        queryClient.invalidateQueries([QUERY_KEYS.POST, variables.postId])
        queryClient.invalidateQueries([QUERY_KEYS.NEWEST_COMMENTS, variables.postId])
        queryClient.invalidateQueries(QUERY_KEYS.POSTS_WITH_DETAILS)
        
        if (data.data.created) {
          toast.success('Comment created successfully!')
        } else {
          toast.info('Comment already exists')
        }
      },
      onError: (error) => {
        const message = error.response?.data?.message || 'Failed to find or create comment'
        toast.error(message)
      }
    }
  )
}

// Users Hooks
export const useUserById = (userId) => {
  return useQuery(
    [QUERY_KEYS.USER, userId],
    () => apiService.getUserById(userId),
    {
      enabled: !!userId,
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  )
}

export const useUserByEmail = (email) => {
  return useQuery(
    [QUERY_KEYS.USER, 'email', email],
    () => apiService.getUserByEmail(email),
    {
      enabled: !!email,
      staleTime: 10 * 60 * 1000,
    }
  )
}

export const useUpdateUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation(
    ({ userId, userData }) => apiService.updateUser(userId, userData),
    {
      onSuccess: (data) => {
        // Invalidate user queries
        queryClient.invalidateQueries([QUERY_KEYS.USER, data.data.user.id])
        queryClient.invalidateQueries(QUERY_KEYS.USER_PROFILE)
        
        toast.success('User updated successfully!')
      },
      onError: (error) => {
        const message = error.response?.data?.message || 'Failed to update user'
        toast.error(message)
      }
    }
  )
}
