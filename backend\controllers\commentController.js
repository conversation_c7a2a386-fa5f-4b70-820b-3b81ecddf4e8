import { Comment, User, Post } from '../models/index.js';
import { Op } from 'sequelize';

// Create Multiple Comments in Bulk
export const createBulkComments = async (req, res) => {
  try {
    const { comments } = req.body; // Array of comment objects
    const userId = req.user.id;

    // Validate that comments is an array
    if (!Array.isArray(comments) || comments.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Comments must be a non-empty array'
      });
    }

    // Add userId to each comment
    const commentsWithUserId = comments.map(comment => ({
      ...comment,
      userId
    }));

    // Bulk create comments
    const createdComments = await Comment.bulkCreate(commentsWithUserId, {
      validate: true,
      returning: true
    });

    // Fetch created comments with user and post information
    const commentsWithDetails = await Comment.findAll({
      where: {
        id: createdComments.map(comment => comment.id)
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        },
        {
          model: Post,
          as: 'post',
          attributes: ['id', 'title']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: `${createdComments.length} comments created successfully`,
      data: {
        comments: commentsWithDetails,
        count: createdComments.length
      }
    });

  } catch (error) {
    console.error('Create bulk comments error:', error);

    // Handle Sequelize validation errors
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during bulk comment creation'
    });
  }
};

// Update Comment Content by ID - Only owner can edit
export const updateComment = async (req, res) => {
  try {
    const { commentId } = req.params;
    const { userId, content } = req.body;
    const requestUserId = req.user.id;

    // Find the comment
    const comment = await Comment.findByPk(commentId);

    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    // Check if the provided userId matches the comment owner or if user is admin
    if (!comment.isOwnedBy(userId) || (userId !== requestUserId && req.user.role !== 'admin')) {
      return res.status(403).json({
        success: false,
        message: 'Access denied - you can only edit your own comments'
      });
    }

    // Update the comment
    await comment.update({ content });

    // Fetch updated comment with details
    const updatedComment = await Comment.findByPk(commentId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        },
        {
          model: Post,
          as: 'post',
          attributes: ['id', 'title']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: 'Comment updated successfully',
      data: {
        comment: updatedComment
      }
    });

  } catch (error) {
    console.error('Update comment error:', error);

    // Handle Sequelize validation errors
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during comment update'
    });
  }
};

// Find or Create Comment for a Specific Post/User/Content
export const findOrCreateComment = async (req, res) => {
  try {
    const { postId, content } = req.body;
    const userId = req.user.id;

    // Verify that the post exists
    const post = await Post.findByPk(postId);
    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    // Find or create comment
    const [comment, created] = await Comment.findOrCreate({
      where: { postId, userId, content },
      defaults: { postId, userId, content },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        },
        {
          model: Post,
          as: 'post',
          attributes: ['id', 'title']
        }
      ]
    });

    res.status(created ? 201 : 200).json({
      success: true,
      message: created ? 'Comment created successfully' : 'Comment already exists',
      data: {
        comment,
        created
      }
    });

  } catch (error) {
    console.error('Find or create comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during comment find or create'
    });
  }
};

// Search Comments Containing a Word & Count Matches
export const searchComments = async (req, res) => {
  try {
    const { word } = req.query;

    const comments = await Comment.findAll({
      where: {
        content: {
          [Op.like]: `%${word}%`
        }
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        },
        {
          model: Post,
          as: 'post',
          attributes: ['id', 'title']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.status(200).json({
      success: true,
      message: `Found ${comments.length} comments containing "${word}"`,
      data: {
        comments,
        count: comments.length,
        searchTerm: word
      }
    });

  } catch (error) {
    console.error('Search comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during comment search'
    });
  }
};

// Get 3 Most Recent Comments for a Post
export const getNewestComments = async (req, res) => {
  try {
    const { postId } = req.params;
    const limit = 3;

    // Verify that the post exists
    const post = await Post.findByPk(postId);
    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    const comments = await Comment.findAll({
      where: { postId },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit
    });

    res.status(200).json({
      success: true,
      message: `Retrieved ${comments.length} newest comments for post`,
      data: {
        comments,
        count: comments.length,
        postId: parseInt(postId)
      }
    });

  } catch (error) {
    console.error('Get newest comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during newest comments retrieval'
    });
  }
};

// Get Comment by ID with User & Post Information
export const getCommentDetails = async (req, res) => {
  try {
    const { id } = req.params;

    const comment = await Comment.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email']
        },
        {
          model: Post,
          as: 'post',
          attributes: ['id', 'title', 'content'],
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });

    if (!comment) {
      return res.status(404).json({
        success: false,
        message: 'Comment not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Comment details retrieved successfully',
      data: {
        comment
      }
    });

  } catch (error) {
    console.error('Get comment details error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during comment details retrieval'
    });
  }
};

// Create single comment (additional endpoint)
export const createComment = async (req, res) => {
  try {
    const { content, postId } = req.body;
    const userId = req.user.id;

    // Verify that the post exists
    const post = await Post.findByPk(postId);
    if (!post) {
      return res.status(404).json({
        success: false,
        message: 'Post not found'
      });
    }

    // Create comment
    const comment = await Comment.create({
      content,
      postId,
      userId
    });

    // Fetch created comment with details
    const commentWithDetails = await Comment.findByPk(comment.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name']
        },
        {
          model: Post,
          as: 'post',
          attributes: ['id', 'title']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Comment created successfully',
      data: {
        comment: commentWithDetails
      }
    });

  } catch (error) {
    console.error('Create comment error:', error);

    // Handle Sequelize validation errors
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during comment creation'
    });
  }
};
