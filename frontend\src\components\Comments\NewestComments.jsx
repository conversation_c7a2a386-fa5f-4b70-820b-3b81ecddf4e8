import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { MessageSquare, User, Calendar, ExternalLink, RefreshCw } from 'lucide-react'
import { useNewestComments } from '../../hooks/useApi'
import { formatRelativeTime, truncateText } from '../../utils/helpers'

const NewestComments = ({ postId, limit = 3, showPostTitle = false }) => {
  const { data: commentsData, isLoading, error, refetch } = useNewestComments(postId)
  const comments = commentsData?.data?.comments || []

  const handleRefresh = () => {
    refetch()
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(limit)].map((_, i) => (
          <div key={i} className="card animate-pulse">
            <div className="card-body">
              <div className="flex space-x-3">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="card">
        <div className="card-body text-center">
          <MessageSquare className="w-12 h-12 text-error-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Failed to load comments
          </h3>
          <p className="text-gray-600 mb-4">
            There was an error loading the newest comments.
          </p>
          <button
            onClick={handleRefresh}
            className="btn btn-secondary btn-sm"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (comments.length === 0) {
    return (
      <div className="card">
        <div className="card-body text-center">
          <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No comments yet
          </h3>
          <p className="text-gray-600">
            Be the first to comment on this post!
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Latest Comments ({comments.length})
        </h3>
        <button
          onClick={handleRefresh}
          className="btn btn-ghost btn-sm"
          title="Refresh comments"
        >
          <RefreshCw className="w-4 h-4" />
        </button>
      </div>

      {/* Comments List */}
      <div className="space-y-3">
        {comments.map((comment) => (
          <NewestCommentCard 
            key={comment.id} 
            comment={comment} 
            showPostTitle={showPostTitle}
          />
        ))}
      </div>

      {/* View All Link */}
      {postId && (
        <div className="text-center">
          <Link
            to={`/posts/${postId}`}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            View all comments
          </Link>
        </div>
      )}
    </div>
  )
}

// Individual Comment Card Component
const NewestCommentCard = ({ comment, showPostTitle = false }) => {
  return (
    <div className="card hover:shadow-soft transition-shadow">
      <div className="card-body py-3">
        <div className="flex space-x-3">
          {/* User Avatar */}
          <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
            <User className="w-4 h-4 text-primary-600" />
          </div>
          
          <div className="flex-1 min-w-0">
            {/* Comment Header */}
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="text-sm font-medium text-gray-900">
                {comment.user?.name || 'Unknown User'}
              </h4>
              <span className="text-xs text-gray-500">
                {formatRelativeTime(comment.createdAt)}
              </span>
            </div>

            {/* Post Title (if shown) */}
            {showPostTitle && comment.post && (
              <div className="text-xs text-gray-500 mb-2">
                on{' '}
                <Link
                  to={`/posts/${comment.post.id}`}
                  className="text-primary-600 hover:text-primary-700"
                >
                  {truncateText(comment.post.title, 40)}
                </Link>
              </div>
            )}

            {/* Comment Content */}
            <div className="text-sm text-gray-700 mb-2">
              {truncateText(comment.content, 100)}
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 text-xs text-gray-500">
                <span className="flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatRelativeTime(comment.createdAt)}
                </span>
              </div>
              
              <Link
                to={`/posts/${comment.postId}#comment-${comment.id}`}
                className="flex items-center space-x-1 text-primary-600 hover:text-primary-700 text-xs"
              >
                <ExternalLink className="w-3 h-3" />
                <span>View</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NewestComments
