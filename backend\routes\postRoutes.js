import express from 'express';
import {
  createPost,
  deletePost,
  getPostsWithDetails,
  getPostsWithCommentCount,
  getAllPosts,
  getPostById
} from '../controllers/postController.js';
import {
  validatePost,
  validatePostId
} from '../middleware/validation.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Post API Endpoints

// 1. Create New Post (using instance + save)
// POST /posts
router.post('/', authenticateToken, validatePost, createPost);

// 2. Delete Post by ID - Only owner can delete
// DELETE /posts/:postId
router.delete('/:postId', authenticateToken, validatePostId, deletePost);

// 3. Get All Posts with User & Comments Details
// GET /posts/details
router.get('/details', getPostsWithDetails);

// 4. Get All Posts with Comment Count
// GET /posts/comment-count
router.get('/comment-count', getPostsWithCommentCount);

// Additional endpoints for better functionality

// Get all posts (general endpoint)
// GET /posts
router.get('/', getAllPosts);

// Get post by ID
// GET /posts/:postId
router.get('/:postId', validatePostId, getPostById);

export default router;
