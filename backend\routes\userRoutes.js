import express from 'express';
import {
  login,
  signUp,
  updateOrCreateUserById,
  findUserByEmail,
  getUserById
} from '../controllers/userController.js';
import {
  validateUserSignup,
  validateUserUpdate,
  validateEmailQuery,
  validateUserId
} from '../middleware/validation.js';

const router = express.Router();

// User API Endpoints

// 0. Login - Authenticate user
// POST /users/login
router.post('/login', validateUserSignup, login);

// 1. Sign Up - Create a new user using build & save
// POST /users/signup
router.post('/signup', validateUserSignup, signUp);

// 2. Update or Create User by ID (Skip validation)
// PUT /users/:id
router.put('/:id', validateUserUpdate, updateOrCreateUserById);

// 3. Find User by Email
// GET /users/by-email?email=<EMAIL>
router.get('/by-email', validateEmailQuery, findUserByEmail);

// 4. Get User by ID (without role field)
// GET /user/:id (Note: different from /users/:id to match the specification)
router.get('/:id', validateUserId, getUserById);

export default router;
