{"name": "backend", "version": "1.0.0", "description": "Backend API for User, Post, and Comment Management", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "sequelize": "^6.35.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}