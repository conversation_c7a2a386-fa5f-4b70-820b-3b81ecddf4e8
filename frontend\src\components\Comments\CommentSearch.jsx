import { useState, useEffect } from 'react'
import { Search, MessageSquare, User, Calendar, ExternalLink } from 'lucide-react'
import { Link } from 'react-router-dom'
import { useSearchComments } from '../../hooks/useApi'
import { formatRelativeTime, truncateText, debounce } from '../../utils/helpers'

const CommentSearch = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const debouncedUpdate = debounce((term) => {
      setDebouncedSearchTerm(term)
    }, 500)

    debouncedUpdate(searchTerm)
  }, [searchTerm])

  const { data: searchData, isLoading, error } = useSearchComments(debouncedSearchTerm)
  const comments = searchData?.data?.comments || []
  const totalCount = searchData?.data?.count || 0

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Search Comments
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Find specific comments across all posts using keywords and phrases.
        </p>
      </div>

      {/* Search Input */}
      <div className="card">
        <div className="card-body">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search comments..."
              className="input pl-10 w-full text-lg"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {searchTerm && (
            <div className="mt-4 text-sm text-gray-600">
              {isLoading ? (
                <div className="flex items-center">
                  <div className="spinner w-4 h-4 mr-2"></div>
                  Searching...
                </div>
              ) : (
                <div>
                  Found {totalCount} comment{totalCount !== 1 ? 's' : ''} 
                  {searchTerm && ` containing "${searchTerm}"`}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Search Results */}
      {searchTerm && (
        <div className="space-y-4">
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="card animate-pulse">
                  <div className="card-body">
                    <div className="flex space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                        <div className="h-4 bg-gray-200 rounded"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <MessageSquare className="w-12 h-12 text-error-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Search Error
              </h3>
              <p className="text-gray-600">
                Failed to search comments. Please try again.
              </p>
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-12">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No comments found
              </h3>
              <p className="text-gray-600">
                Try different keywords or check your spelling.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment) => (
                <CommentSearchResult key={comment.id} comment={comment} searchTerm={searchTerm} />
              ))}
            </div>
          )}
        </div>
      )}

      {/* Search Tips */}
      {!searchTerm && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Search Tips</h3>
          </div>
          <div className="card-body">
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Use specific keywords to find relevant comments</li>
              <li>• Search is case-insensitive</li>
              <li>• Results show comments from all posts you have access to</li>
              <li>• Click on any result to view the full post and comment</li>
              <li>• Use quotes for exact phrase matching</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  )
}

// Comment Search Result Component
const CommentSearchResult = ({ comment, searchTerm }) => {
  // Highlight search term in content
  const highlightText = (text, term) => {
    if (!term) return text
    
    const regex = new RegExp(`(${term})`, 'gi')
    const parts = text.split(regex)
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    )
  }

  return (
    <div className="card hover:shadow-medium transition-shadow">
      <div className="card-body">
        <div className="flex space-x-3">
          {/* User Avatar */}
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
            <User className="w-5 h-5 text-primary-600" />
          </div>
          
          <div className="flex-1 min-w-0">
            {/* Comment Header */}
            <div className="flex items-center space-x-2 mb-2">
              <h4 className="font-medium text-gray-900">
                {comment.user?.name || 'Unknown User'}
              </h4>
              <span className="text-sm text-gray-500">
                commented {formatRelativeTime(comment.createdAt)}
              </span>
            </div>

            {/* Comment Content */}
            <div className="text-gray-700 mb-3">
              {highlightText(truncateText(comment.content, 200), searchTerm)}
            </div>

            {/* Post Info */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span>on post:</span>
                <Link
                  to={`/posts/${comment.post?.id}`}
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  {truncateText(comment.post?.title || 'Untitled Post', 50)}
                </Link>
              </div>
              
              <Link
                to={`/posts/${comment.post?.id}#comment-${comment.id}`}
                className="flex items-center space-x-1 text-primary-600 hover:text-primary-700 text-sm"
              >
                <ExternalLink className="w-4 h-4" />
                <span>View</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CommentSearch
