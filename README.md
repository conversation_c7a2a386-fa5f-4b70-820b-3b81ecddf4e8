# User, Post, and Comment Management System

A comprehensive full-stack application built with **Node.js**, **Express**, **Sequelize ORM**, **React**, and **Vite** for managing users, posts, and comments with a modern, beautiful user interface.

## 🚀 Features

### Backend Features
- **User Management**: Registration, authentication, profile management
- **Post Management**: CRUD operations with soft delete (paranoid mode)
- **Comment System**: Nested comments, bulk operations, search functionality
- **Database Models**: Sequelize ORM with relationships, validations, and hooks
- **RESTful APIs**: Complete set of endpoints with proper access control
- **Data Integrity**: Email validation, password strength, ownership checks

### Frontend Features
- **Modern UI**: Built with React and Tailwind CSS
- **Responsive Design**: Works on all screen sizes
- **Real-time Updates**: Dynamic content updates
- **User Authentication**: Login/signup with JWT tokens
- **Interactive Dashboard**: User statistics and activity overview
- **Search & Filter**: Advanced search capabilities

## 📁 Project Structure

```
├── backend/                 # Express.js API server
│   ├── config/             # Database configuration
│   ├── models/             # Sequelize models
│   ├── routes/             # API routes
│   ├── middleware/         # Custom middleware
│   └── server.js           # Main server file
├── frontend/               # React + Vite application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── utils/          # Utility functions
│   │   └── App.jsx         # Main App component
└── package.json            # Root package.json
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- MySQL database
- npm or yarn

### Quick Start

1. **Clone and install dependencies:**
```bash
npm run install:all
```

2. **Setup environment variables:**
```bash
cd backend
cp .env.example .env
# Edit .env with your database credentials
```

3. **Start development servers:**
```bash
npm run dev
```

This will start both backend (port 5000) and frontend (port 5173) servers.

## 🔧 Available Scripts

- `npm run dev` - Start both backend and frontend in development mode
- `npm run dev:backend` - Start only backend server
- `npm run dev:frontend` - Start only frontend server
- `npm run build` - Build frontend for production
- `npm run start` - Start backend in production mode

## 📚 API Endpoints

### User APIs
- `POST /users/signup` - Create new user
- `PUT /users/:id` - Update or create user by ID
- `GET /users/by-email` - Find user by email
- `GET /user/:id` - Get user by ID (without role)

### Post APIs
- `POST /posts` - Create new post
- `DELETE /posts/:postId` - Delete post (owner only)
- `GET /posts/details` - Get all posts with user & comments
- `GET /posts/comment-count` - Get posts with comment count

### Comment APIs
- `POST /comments` - Create multiple comments in bulk
- `PATCH /comments/:commentId` - Update comment (owner only)
- `POST /comments/find-or-create` - Find or create comment
- `GET /comments/search` - Search comments by keyword
- `GET /comments/newest/:postId` - Get 3 most recent comments
- `GET /comments/details/:id` - Get comment with user & post info

## 🎨 Technologies Used

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **Sequelize** - ORM for database operations
- **MySQL** - Database
- **JWT** - Authentication
- **bcryptjs** - Password hashing

### Frontend
- **React** - UI library
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **React Query** - Data fetching and caching
- **React Hook Form** - Form handling
- **Lucide React** - Modern icons

## 🔒 Security Features

- Password hashing with bcryptjs
- JWT token authentication
- Input validation and sanitization
- Role-based access control
- Ownership verification for sensitive operations

## 📝 License

MIT License - see LICENSE file for details.
